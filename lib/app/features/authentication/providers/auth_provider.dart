import 'dart:async';
import 'dart:developer';

import 'package:eljunto/app/core/services/notification_service.dart';
import 'package:eljunto/app/features/authentication/models/auth_models.dart';
import 'package:eljunto/app/features/authentication/services/auth_service.dart';
import 'package:eljunto/app/features/authentication/utils/auth_utils.dart';
import 'package:eljunto/controller/subscription_controller.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

/// Consolidated Authentication Provider
/// Manages all authentication state, form validation, and business logic
class AuthProvider with ChangeNotifier {
  final AuthService _authService = AuthService();
  final NotificationServices _notificationServices = NotificationServices();

  // Authentication State
  AuthState _authState = AuthState.initial;
  AuthStep _currentStep = AuthStep.login;
  String _errorMessage = '';
  bool _isSuccess = false;

  // Form States
  bool _isLoading = false;
  bool _isFieldEnabled = true;

  // Login specific state
  bool _loginLoading = false;

  // Signup specific state
  bool _signupLoading = false;
  bool _agreeToPolicy = false;
  bool _is18YearsOld = false;
  bool _showPolicyValidation = true;
  bool _showAgeValidation = true;

  // OTP specific state
  bool _otpLoading = false;
  bool _isResendOtp = false;
  String _currentToken = '';
  int _otpTimer = 0;
  Timer? _timer;
  ValueNotifier<int> timerCountdown = ValueNotifier<int>(0);

  // Password specific state
  bool _passwordLoading = false;
  bool _setPasswordFlag = false;
  bool _confirmPasswordFlag = false;
  bool _isPasswordValid = false;
  bool _isPasswordComplex = false;
  bool _isPasswordMatch = false;

  // Profile setup specific state
  final bool _nameLoading = false;
  bool _handleLoading = false;
  bool _nameFlag = false;
  bool _locationFlag = false;
  bool _bioFlag = false;
  bool _handlerFlag = false;
  bool _isHandleAvailable = false;

  // Getters
  AuthState get authState => _authState;

  AuthStep get currentStep => _currentStep;

  String get errorMessage => _errorMessage;

  bool get isSuccess => _isSuccess;

  bool get isLoading => _isLoading;

  bool get isFieldEnabled => _isFieldEnabled;

  // Login getters
  bool get loginLoading => _loginLoading;

  // Signup getters
  bool get signupLoading => _signupLoading;

  bool get agreeToPolicy => _agreeToPolicy;

  bool get is18YearsOld => _is18YearsOld;

  bool get showPolicyValidation => _showPolicyValidation;

  bool get showAgeValidation => _showAgeValidation;

  // OTP getters
  bool get otpLoading => _otpLoading;

  bool get isResendOtp => _isResendOtp;

  String get currentToken => _currentToken;

  int get otpTimer => _otpTimer;

  // Password getters
  bool get passwordLoading => _passwordLoading;

  bool get setPasswordFlag => _setPasswordFlag;

  bool get confirmPasswordFlag => _confirmPasswordFlag;

  bool get isPasswordValid => _isPasswordValid;

  bool get isPasswordComplex => _isPasswordComplex;

  bool get isPasswordMatch => _isPasswordMatch;

  // Profile setup getters
  bool get nameLoading => _nameLoading;

  bool get handleLoading => _handleLoading;

  bool get nameFlag => _nameFlag;

  bool get locationFlag => _locationFlag;

  bool get bioFlag => _bioFlag;

  bool get handlerFlag => _handlerFlag;

  bool get isHandleAvailable => _isHandleAvailable;

  // Setters
  set agreeToPolicy(bool value) {
    if (_agreeToPolicy != value) {
      _agreeToPolicy = value;
      notifyListeners();
    }
  }

  set is18YearsOld(bool value) {
    if (_is18YearsOld != value) {
      _is18YearsOld = value;
      notifyListeners();
    }
  }

  set nameFlag(bool value) {
    if (_nameFlag != value) {
      _nameFlag = value;
      notifyListeners();
    }
  }

  set locationFlag(bool value) {
    if (_locationFlag != value) {
      _locationFlag = value;
      notifyListeners();
    }
  }

  set setBioFlag(bool value) {
    if (_bioFlag != value) {
      _bioFlag = value;
      notifyListeners();
    }
  }

  set handlerFlag(bool value) {
    if (_handlerFlag != value) {
      _handlerFlag = value;
      notifyListeners();
    }
  }

  // Initialize provider
  void initialize() {
    _authState = AuthState.initial;
    _currentStep = AuthStep.login;
    _errorMessage = '';
    _isSuccess = false;
    _isLoading = false;
    _isFieldEnabled = true;

    // Reset signup state
    _agreeToPolicy = false;
    _is18YearsOld = false;
    _showPolicyValidation = true;
    _showAgeValidation = true;

    notifyListeners();
  }

  // Clear error message
  void clearError() {
    _errorMessage = '';
    notifyListeners();
  }

  // Login Methods
  Future<bool> login(
      String email, String password, BuildContext context) async {
    _loginLoading = true;
    _isFieldEnabled = false;
    _errorMessage = '';
    notifyListeners();

    try {
      // Get FCM token
      String? fcmToken = await _notificationServices.getDeviceToken();
      await AuthHelpers.saveFcmToken(fcmToken);

      // Get device ID
      String deviceId = await AuthHelpers.getDeviceId();

      // Create login request
      final loginRequest = LoginRequest(
        email: email.toLowerCase().trim(),
        password: password.trim(),
        fcmToken: fcmToken,
        deviceId: deviceId,
      );

      // Call login API
      final response = await _authService.login(loginRequest);

      if (response.success) {
        _isSuccess = true;
        _authState = AuthState.authenticated;

        // Get subscription details
        if (context.mounted) {
          await Provider.of<SubscriptionController>(context, listen: false)
              .getSubscriptionDetails(context);
        }
      } else {
        _isSuccess = false;
        _authState = AuthState.error;
        _errorMessage = AuthHelpers.getAuthErrorMessage(
          response.statusCode,
          response.message,
        );
      }
    } catch (e) {
      _isSuccess = false;
      _authState = AuthState.error;
      _errorMessage = 'Something went wrong';
      log('Login error: $e');
    } finally {
      _loginLoading = false;
      _isFieldEnabled = true;
      notifyListeners();
    }

    return _isSuccess;
  }

  // Signup Methods
  Future<void> submitSignupForm(
    String userEmail,
    GlobalKey<FormState> formKey,
    BuildContext context,
  ) async {
    bool validation = formKey.currentState!.validate();

    _showPolicyValidation = !_agreeToPolicy;
    _showAgeValidation = !_is18YearsOld;
    notifyListeners();

    if (userEmail.isEmpty && validation) {
      _errorMessage = '*Enter email';
      notifyListeners();
      return;
    }

    if (!AuthValidators.validateEmail(userEmail) && validation) {
      _errorMessage = '*Email incorrect';
      notifyListeners();
      return;
    }

    if (validation && _agreeToPolicy && _is18YearsOld && userEmail.isNotEmpty) {
      String emailId = userEmail.toLowerCase();
      _signupLoading = true;
      _isFieldEnabled = false;
      _errorMessage = '';
      notifyListeners();

      try {
        final signupRequest = SignupRequest(email: emailId);
        final response = await _authService.verifyEmail(signupRequest);

        await AuthHelpers.saveEmailLocally(userEmail);

        if (response.success && response.token != null) {
          _currentToken = response.token!;
          _currentStep = AuthStep.otpVerification;

          if (context.mounted) {
            context.pushNamed(
              'otp',
              extra: {
                'email': emailId,
                'token': response.token,
                'isForgotPassword': false,
              },
            );
          }
        } else {
          _errorMessage = AuthHelpers.getAuthErrorMessage(
            response.statusCode,
            response.message,
          );
        }
      } catch (e) {
        _errorMessage = 'Something went wrong';
        log('Signup error: $e');
      } finally {
        _signupLoading = false;
        _isFieldEnabled = true;
        notifyListeners();
      }
    }
  }

  // OTP Methods
  Future<void> verifyOtp(
    String email,
    String otp,
    bool isForgotPassword,
    String token,
    BuildContext context,
  ) async {
    if (!AuthValidators.validateOtp(otp)) {
      _errorMessage = 'Please enter a valid 6-digit OTP';
      notifyListeners();
      return;
    }

    _otpLoading = true;
    _isFieldEnabled = false;
    _errorMessage = '';
    notifyListeners();

    try {
      final otpRequest = OtpRequest(
        email: email,
        token: token,
        otp: otp,
        isForgotPassword: isForgotPassword,
      );

      final response = await _authService.verifyOtp(otpRequest);

      if (response.success) {
        _currentStep = AuthStep.passwordSetup;

        if (context.mounted) {
          if (isForgotPassword) {
            context.pushReplacementNamed(
              'reset-password',
              extra: {
                'email': email,
                'token': token,
                'otp': otp,
                'isForgotPassword': isForgotPassword,
              },
            );
          } else {
            context.pushReplacementNamed(
              'set-password',
              extra: {
                'email': email,
                'token': token,
                'otp': otp,
                'isForgotPassword': isForgotPassword,
              },
            );
          }
        }
      } else {
        _errorMessage = AuthHelpers.getAuthErrorMessage(
          response.statusCode,
          response.message,
        );
      }
    } catch (e) {
      _errorMessage = 'Something went wrong';
      log('OTP verification error: $e');
    } finally {
      _otpLoading = false;
      _isFieldEnabled = true;
      notifyListeners();
    }
  }

  // Password Methods
  void validatePassword(String password) {
    _setPasswordFlag = false;
    _isPasswordValid = AuthValidators.validatePassword(password);
    _isPasswordComplex = AuthValidators.validatePasswordComplexity(password);
    notifyListeners();
  }

  void validateConfirmPassword(String confirmPassword, String password) {
    _confirmPasswordFlag = false;
    _isPasswordMatch =
        AuthValidators.validatePasswordMatch(password, confirmPassword);
    notifyListeners();
  }

  Future<void> setPassword(
    String userEmail,
    String token,
    String otp,
    String password,
    String confirmPassword,
    bool isForgotPassword,
    BuildContext context,
  ) async {
    if (!_isPasswordValid || !_isPasswordComplex) {
      _setPasswordFlag = true;
      notifyListeners();
      return;
    }

    if (!_isPasswordMatch) {
      _confirmPasswordFlag = true;
      notifyListeners();
      return;
    }

    _passwordLoading = true;
    _isFieldEnabled = false;
    _errorMessage = '';
    notifyListeners();

    try {
      if (isForgotPassword) {
        final resetRequest = PasswordResetRequest(
          email: userEmail,
          token: token,
          otp: otp,
          password: password,
        );

        final response = await _authService.resetPassword(resetRequest);

        if (response.success) {
          if (context.mounted) {
            context.pushReplacementNamed('login');
          }
        } else {
          _errorMessage = AuthHelpers.getAuthErrorMessage(
            response.statusCode,
            response.message,
          );
        }
      } else {
        // Get FCM token and device ID
        String? fcmToken = await AuthHelpers.getFcmToken();
        String deviceId = await AuthHelpers.getDeviceId();

        final setupRequest = PasswordSetupRequest(
          email: userEmail,
          token: token,
          otp: otp,
          password: password,
          fcmToken: fcmToken ?? '',
          deviceId: deviceId,
        );

        final response = await _authService.setPassword(setupRequest);

        if (response.success) {
          _currentStep = AuthStep.profileSetup;
          // Save user details locally
          await AuthHelpers.saveUserDetails(
            data: response.data!,
            context: context,
          );

          if (context.mounted) {
            context.pushReplacementNamed(
              'set-name-handle',
              extra: {
                'email': userEmail,
                'token': token,
                'otp': otp,
              },
            );
          }
        } else {
          _errorMessage = AuthHelpers.getAuthErrorMessage(
            response.statusCode,
            response.message,
          );
        }
      }
    } catch (e) {
      _errorMessage = 'An unexpected error occurred.';
      log('Password set/reset error: $e');
    } finally {
      _passwordLoading = false;
      _isFieldEnabled = true;
      notifyListeners();
    }
  }

  // Profile Setup Methods
  void submitName(
    String name,
    String location,
    String bio,
    GlobalKey<FormState> nameFormKey,
  ) {
    // Reset all flags
    _nameFlag = false;
    _locationFlag = false;
    _bioFlag = false;

    bool isValid = true;

    // Validate name
    if (name.trim().isEmpty) {
      _nameFlag = true;
      isValid = false;
    }

    // Validate location
    if (location.trim().isEmpty) {
      _locationFlag = true;
      isValid = false;
    }

    // Validate bio
    if (bio.trim().isEmpty) {
      _bioFlag = true;
      isValid = false;
    }

    // If form is valid, trigger form validation for any additional rules
    if (isValid && nameFormKey.currentState != null) {
      isValid = nameFormKey.currentState!.validate();
      if (!isValid) {
        // If form validation fails, ensure we show appropriate error states
        _nameFlag = true; // Assuming name validation failed
      }
    }

    notifyListeners();
  }

  Future<void> checkHandleAvailability(String email, String handle) async {
    if (!AuthValidators.validateUserHandle(handle)) {
      _isHandleAvailable = false;
      _handlerFlag = true;
      notifyListeners();
      return;
    }

    try {
      final request = HandleAvailabilityRequest(
        email: email,
        userHandle: '@$handle',
      );

      final response = await _authService.checkHandleAvailability(request);
      _isHandleAvailable = response.success;
      _handlerFlag = !response.success;
      _errorMessage = response.message ?? '';
      notifyListeners();
    } catch (e) {
      _isHandleAvailable = false;
      _handlerFlag = true;
      notifyListeners();
      log('Handle availability check error: $e');
    }
  }

  Future<void> submitHandle(
    String name,
    String location,
    String bio,
    String handle,
    String userEmail,
    String token,
    String otp,
    GlobalKey<FormState> handleFormKey,
    BuildContext context,
  ) async {
    if (handleFormKey.currentState!.validate()) {
      if (handle.isEmpty) {
        _errorMessage = '*Enter handle';
        _handlerFlag = true;
        notifyListeners();
        return;
      }

      if (!_isHandleAvailable) {
        _handlerFlag = true;
        notifyListeners();
        return;
      }
    }

    _handleLoading = true;
    _isFieldEnabled = false;
    _errorMessage = '';
    notifyListeners();

    try {
      final profileRequest = ProfileSetupRequest(
        email: userEmail,
        token: token,
        otp: otp,
        userName: name,
        userHandle: '@$handle',
        userLocation: location,
        userBio: bio,
      );

      final response = await _authService.setNameAndHandle(profileRequest);

      if (response.success) {
        _currentStep = AuthStep.completed;
        _authState = AuthState.authenticated;

        if (context.mounted) {
          context.pushReplacementNamed('subscription');
        }
      } else {
        _errorMessage = AuthHelpers.getAuthErrorMessage(
          response.statusCode,
          response.message,
        );
      }
    } catch (e) {
      _errorMessage = 'Something went wrong';
      log('Profile setup error: $e');
    } finally {
      _handleLoading = false;
      _isFieldEnabled = true;
      notifyListeners();
    }
  }

  Future<void> logout(BuildContext context) async {
    _isLoading = true;
    notifyListeners();
    try {
      // Perform logout actions such as clearing session, tokens, etc.
      await AuthHelpers.clearUserSession();
      _authState = AuthState.initial;
      _currentStep = AuthStep.login;
      if (context.mounted) {
        context.go('/login');
      }
    } catch (e) {
      log('Logout error: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Timer Methods
  void startTimer() {
    _otpTimer = AuthConstants.otpTimerDuration;
    _isResendOtp = false;
    _timer?.cancel();

    timerCountdown.value = _otpTimer;

    const oneSec = Duration(seconds: 1);
    _timer = Timer.periodic(oneSec, (timer) {
      if (_otpTimer == AuthConstants.otpExpiryTime) {
        _isResendOtp = true;
        _timer!.cancel();
      } else {
        _isResendOtp = false;
        _otpTimer--;
      }
      timerCountdown.value = _otpTimer;
      notifyListeners();
    });
  }

  void stopTimer() {
    _timer?.cancel();
    _isResendOtp = false;
    _otpTimer = 0;
    notifyListeners();
  }

  @override
  void dispose() {
    _timer?.cancel();
    timerCountdown.dispose();
    super.dispose();
  }
}
