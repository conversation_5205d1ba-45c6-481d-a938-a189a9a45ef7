import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:crypto/crypto.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:eljunto/controller/user_credential_controller.dart';
import 'package:email_validator/email_validator.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Authentication Constants
class AuthConstants {
  static const int otpExpiryTime = 0;
  static const int otpTimerDuration = 600;
  static const int minPasswordLength = 8;
  static const String fcmTokenKey = 'fcmToken';
  static const String jwtTokenKey = 'jwttoken';
  static const String userIdKey = 'userId';
  static const String userEmailKey = 'userEmail';
  static const String userNameKey = 'userName';
  static const String userHandleKey = 'userHandle';
  static const String deviceIdKey = 'deviceId';
}

// Authentication Validators
class AuthValidators {
  /// Validate email format
  static bool validateEmail(String email) {
    if (email.isEmpty) return false;
    return EmailValidator.validate(email);
  }

  /// Validate password strength
  static bool validatePassword(String password) {
    if (password.isEmpty || password.length < AuthConstants.minPasswordLength) {
      return false;
    }
    return true;
  }

  /// Validate password complexity
  static bool validatePasswordComplexity(String password) {
    if (password.isEmpty) return false;

    // Check for at least one uppercase letter, one lowercase letter, one digit, and one special character
    final hasUppercase = RegExp(r'[A-Z]');
    final hasLowercase = RegExp(r'[a-z]');
    final hasDigit = RegExp(r'[0-9]');
    final hasSymbol = RegExp(r'[\W_]');

    int criteriaMet = 0;

    if (hasUppercase.hasMatch(password)) criteriaMet++;
    if (hasLowercase.hasMatch(password)) criteriaMet++;
    if (hasDigit.hasMatch(password)) criteriaMet++;
    if (hasSymbol.hasMatch(password)) criteriaMet++;

    return criteriaMet >= 3;
  }

  /// Validate password match
  static bool validatePasswordMatch(String password, String confirmPassword) {
    return password.isNotEmpty && password == confirmPassword;
  }

  /// Validate OTP format
  static bool validateOtp(String otp) {
    if (otp.isEmpty || otp.length != 6) return false;
    return RegExp(r'^[0-9]{6}$').hasMatch(otp);
  }

  /// Validate user handle format
  static bool validateUserHandle(String handle) {
    if (handle.isEmpty || handle.length < 3) return false;
    // Allow alphanumeric characters and underscores, no spaces
    return RegExp(r'^[a-zA-Z0-9_]+$').hasMatch(handle);
  }

  /// Validate user name
  static bool validateUserName(String name) {
    return name.trim().isNotEmpty && name.trim().length >= 2;
  }
}

// Authentication Helpers
class AuthHelpers {
  /// Encrypt password using SHA256
  static String encryptPassword(String password) {
    var bytes = utf8.encode(password);
    var digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Get device ID
  static Future<String> getDeviceId() async {
    try {
      DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      if (Platform.isAndroid) {
        AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
        return androidInfo.id;
      } else if (Platform.isIOS) {
        IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
        return iosInfo.identifierForVendor ?? '';
      }
    } catch (e) {
      log('Error getting device ID: $e');
    }
    return '';
  }

  /// Save authentication data to local storage
  static Future<void> saveAuthData(Map<String, dynamic> authData) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();

      // Save JWT token
      if (authData['token'] != null) {
        await prefs.setString(AuthConstants.jwtTokenKey, authData['token']);
      }

      // Save user data
      if (authData['userId'] != null) {
        await prefs.setInt(AuthConstants.userIdKey, authData['userId']);
      }

      if (authData['userEmailId'] != null) {
        await prefs.setString(
            AuthConstants.userEmailKey, authData['userEmailId']);
      }

      if (authData['userName'] != null) {
        await prefs.setString(AuthConstants.userNameKey, authData['userName']);
      }

      if (authData['userHandle'] != null) {
        await prefs.setString(
            AuthConstants.userHandleKey, authData['userHandle']);
      }

      log('Authentication data saved successfully');
    } catch (e) {
      log('Error saving authentication data: $e');
    }
  }

  /// Get FCM token from local storage
  static Future<String?> getFcmToken() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      return prefs.getString(AuthConstants.fcmTokenKey);
    } catch (e) {
      log('Error getting FCM token: $e');
      return null;
    }
  }

  /// Save FCM token to local storage
  static Future<void> saveFcmToken(String token) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setString(AuthConstants.fcmTokenKey, token);
      log('FCM token saved successfully');
    } catch (e) {
      log('Error saving FCM token: $e');
    }
  }

  /// Save email locally for signup flow
  static Future<void> saveEmailLocally(String email) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setString('signupEmail', email);
    } catch (e) {
      log('Error saving email locally: $e');
    }
  }

  /// Get saved email from local storage
  static Future<String?> getSavedEmail() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      return prefs.getString('signupEmail');
    } catch (e) {
      log('Error getting saved email: $e');
      return null;
    }
  }

  /// Clear authentication data
  static Future<void> clearAuthData() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.remove(AuthConstants.jwtTokenKey);
      await prefs.remove(AuthConstants.userIdKey);
      await prefs.remove(AuthConstants.userEmailKey);
      await prefs.remove(AuthConstants.userNameKey);
      await prefs.remove(AuthConstants.userHandleKey);
      await prefs.remove('signupEmail');
      log('Authentication data cleared successfully');
    } catch (e) {
      log('Error clearing authentication data: $e');
    }
  }

  static Future<void> clearUserSession() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.clear();
      log('User session cleared successfully');
    } catch (e) {
      log('Error clearing user session: $e');
    }
  }

  /// Format timer display
  static String formatTimer(int seconds) {
    int minutes = seconds ~/ 60;
    int remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  /// Generate error message for authentication failures
  static String getAuthErrorMessage(int statusCode, String? message) {
    switch (statusCode) {
      case 400:
        return message ?? 'Invalid request. Please check your input.';
      case 401:
        return 'Invalid credentials. Please try again.';
      case 403:
        return 'Access denied. Please contact support.';
      case 404:
        return 'Service not found. Please try again later.';
      case 409:
        return message ?? 'Conflict occurred. Please try again.';
      case 429:
        return 'Too many requests. Please wait and try again.';
      case 500:
        return 'Server error. Please try again later.';
      default:
        return message ?? 'Something went wrong. Please try again.';
    }
  }

  /// Save user details after profile setup
  static Future<void> saveUserDetails({
    required Map<String, dynamic> data,
    required BuildContext context,
  }) async {
    try {
      SharedPreferences pref = await SharedPreferences.getInstance();
      pref.setString('jwttoken', data['token']);
      pref.setInt('userId', data['userId']);
      pref.setString('userEmailId', data['userEmailId']);
      pref.setBool('isUserNameAvailable', data['isUserNameAvailable']);
      pref.setBool(
        'isUserBioAndLocationAvailable',
        data['isUserBioAndLocationAvailable'],
      );
      if (data['userHandle'] != null) {
        pref.setString('userHandle', data['userHandle']);
      }

      if (data['userName'] != null) {
        pref.setString('userName', data['userName']);
      }
      // Update user credentials in the provider
      if (context.mounted) {
        await context.read<UserCredentialController>().setUser();
      }
    } catch (e) {
      log('Error saving user details: $e');
    }
  }
}
