import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:in_app_purchase/in_app_purchase.dart';

import '../models/subscription_models.dart';
import '../services/subscription_service.dart';

class SubscriptionProvider extends ChangeNotifier {
  final SubscriptionService _subscriptionService;

  SubscriptionProvider(this._subscriptionService);

  // State from SubscriptionController
  SubscriptionModel? _subscriptionDetails;
  SubscriptionModel? get subscriptionDetails => _subscriptionDetails;

  VerifySubscriptionModel? _verifySubscriptionModel;
  VerifySubscriptionModel? get verifySubscriptionModel =>
      _verifySubscriptionModel;

  bool _isFreeTrial = false;
  bool get isFreeTrial => _isFreeTrial;

  bool _isPurchaseApp = false;
  bool get isPurchaseApp => _isPurchaseApp;

  bool _isApplePurchaseApp = false;
  bool get isApplePurchaseApp => _isApplePurchaseApp;

  List<String> _subscriptionDetailList = [];
  List<String> get subscriptionDetailList => _subscriptionDetailList;

  bool _verifySubscription = true;
  bool get verifySubscription => _verifySubscription;

  // State from InAppPurchaseController
  final InAppPurchase _inAppPurchase = InAppPurchase.instance;
  late StreamSubscription<List<PurchaseDetails>> _streamSubscription;
  Stream<List<PurchaseDetails>> get purchaseStream =>
      _inAppPurchase.purchaseStream;

  List<ProductDetails> _products = [];
  List<ProductDetails> get products => _products;

  List<ProductDetails> _displayProduct = [];
  List<ProductDetails> get displayProduct => _displayProduct;

  final List<ProductDetails> _freeProduct = [];
  List<ProductDetails> get freeProduct => _freeProduct;

  ProductDetails? _purchaseProduct;
  ProductDetails? get purchaseProduct => _purchaseProduct;

  bool _isLoading = false;
  bool get isLoading => _isLoading;

  bool _isPurchaseLoading = false;
  bool get isPurchaseLoading => _isPurchaseLoading;

  bool _isSubscribedClicked = false;
  bool get isSubscribedClicked => _isSubscribedClicked;

  String _planPrice = '';
  String get planPrice => _planPrice;

  void initialize() {
    _streamSubscription = purchaseStream.listen(
      (purchaseDetailsList) {
        _handlePurchaseUpdates(purchaseDetailsList);
      },
      onDone: () => _streamSubscription.cancel(),
      onError: (error) => log("Purchase stream error: $error"),
    );
  }

  @override
  void dispose() {
    _streamSubscription.cancel();
    super.dispose();
  }

  Future<void> getSubscriptionDetails() async {
    _isLoading = true;
    notifyListeners();
    final osType = Platform.isAndroid ? 'google' : 'apple';
    try {
      final response =
          await _subscriptionService.getSubscriptionDetails(osType);
      if (response.statusCode == 200) {
        _subscriptionDetails = SubscriptionModel.fromJson(response.data);
        _isFreeTrial = _subscriptionDetails?.data?.isFreeTrial ?? false;
        _updateSubscriptionDetailList();
        log("Subscription details fetched: ${_subscriptionDetails?.data}");
      } else {
        _subscriptionDetails = null;
        log("Failed to fetch subscription details: ${response.data}");
      }
    } catch (e) {
      _subscriptionDetails = null;
      log("Error fetching subscription details: $e");
    }
    _isLoading = false;
    notifyListeners();
  }

  void _updateSubscriptionDetailList() {
    _subscriptionDetailList = _subscriptionDetails?.data?.subscriptionDetails
            ?.map((e) => e.productId ?? '')
            .where((id) => id.isNotEmpty)
            .toList() ??
        [];
    notifyListeners();
  }

  Future<void> fetchProducts() async {
    if (!await _inAppPurchase.isAvailable()) {
      log("[IAP] Store not available.");
      _products = [];
      notifyListeners();
      return;
    }

    log("[IAP] Store is available. Fetching products...");
    try {
      if (_subscriptionDetailList.isEmpty) {
        await getSubscriptionDetails();
      }
      if (_subscriptionDetailList.isEmpty) {
        throw "No product IDs available from the backend.";
      }

      final response = await _inAppPurchase
          .queryProductDetails(_subscriptionDetailList.toSet());
      if (response.error != null) throw response.error!;
      if (response.productDetails.isEmpty) {
        throw "No products found. Invalid IDs or setup issue.";
      }

      log("[IAP] Products found: ${response.productDetails.map((p) => p.id).toList()}");
      _products = response.productDetails;
      if (_products.isNotEmpty) {
        _planPrice = _products.first.price;
      }
    } catch (e) {
      log("[IAP] Error fetching products: $e");
      _products = [];
    }
    notifyListeners();
  }

  Future<void> buyProduct(ProductDetails product) async {
    final purchaseParam = PurchaseParam(productDetails: product);
    try {
      await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
    } catch (e) {
      log("Error buying product: $e");
    }
  }

  Future<void> _handlePurchaseUpdates(
      List<PurchaseDetails> purchaseDetailsList) async {
    for (var purchaseDetails in purchaseDetailsList) {
      if (purchaseDetails.status == PurchaseStatus.pending) {
        _setPurchaseLoading(true);
      } else {
        _setPurchaseLoading(false);
        if (purchaseDetails.status == PurchaseStatus.error) {
          log("Purchase error: ${purchaseDetails.error?.message}");
        } else if (purchaseDetails.status == PurchaseStatus.purchased ||
            purchaseDetails.status == PurchaseStatus.restored) {
          await _processPurchase(purchaseDetails);
        }
        if (purchaseDetails.pendingCompletePurchase) {
          await _inAppPurchase.completePurchase(purchaseDetails);
        }
      }
    }
  }

  void handlePurchaseUpdates(
      List<PurchaseDetails> purchaseDetailsList, BuildContext context) {
    _handlePurchaseUpdates(purchaseDetailsList);
  }

  Future<void> _processPurchase(PurchaseDetails purchaseDetails) async {
    bool success = false;
    if (Platform.isAndroid) {
      success = await _processAndroidPurchase(purchaseDetails);
    } else if (Platform.isIOS) {
      success = await _processApplePurchase(purchaseDetails);
    }

    if (success) {
      log('Subscription purchase successful.');
      await isActiveSubscription();
      // Potentially navigate to a success screen
    } else {
      log('Subscription purchase failed.');
    }
    notifyListeners();
  }

  Future<bool> _processAndroidPurchase(PurchaseDetails purchaseDetails) async {
    final subscriptionData =
        jsonDecode(purchaseDetails.verificationData.localVerificationData);
    final subscriptionId =
        _getSubscriptionIdForProductId(purchaseDetails.productID);

    if (subscriptionId == null) {
      log("Error: Could not find a matching subscription ID for product ID: ${purchaseDetails.productID}");
      return false;
    }

    final payload = SubscriptionResponseModel(
      productId: purchaseDetails.productID,
      purchasedId: purchaseDetails.purchaseID,
      transactionDate:
          int.tryParse(purchaseDetails.transactionDate ?? '0') ?? 0,
      subscriptionId: subscriptionId,
      verificationSource: purchaseDetails.verificationData.source,
      verificationStatus: purchaseDetails.status.toString(),
      verificationPendingCompletePurchase:
          purchaseDetails.pendingCompletePurchase,
      verificationError: purchaseDetails.error,
      localVerificationData: LocalVerificationData(
        acknowledged: subscriptionData['acknowledged'],
        orderId: subscriptionData['orderId'],
        packageName: subscriptionData['packageName'],
        productId: subscriptionData['productId'],
        purchaseState: subscriptionData['purchaseState'],
        purchaseTime: subscriptionData['purchaseTime'],
        quantity: subscriptionData['quantity'],
        purchaseToken: subscriptionData['purchaseToken'],
        autoRenewing: subscriptionData['autoRenewing'],
      ),
    );

    try {
      final response =
          await _subscriptionService.googlePlaypurchaseResponse(payload);
      _isPurchaseApp = response.statusCode == 200;
      return _isPurchaseApp;
    } catch (e) {
      log("Error processing Android purchase: $e");
      _isPurchaseApp = false;
      return false;
    }
  }

  Future<bool> _processApplePurchase(PurchaseDetails purchaseDetails) async {
    final payload = ApplePurchaseResponseModel(
      localVerificationData:
          purchaseDetails.verificationData.localVerificationData,
      productId: purchaseDetails.productID,
      purchaseId: purchaseDetails.purchaseID,
      purchaseStatus: purchaseDetails.status.toString(),
      serverVerificationData:
          purchaseDetails.verificationData.serverVerificationData,
      source: purchaseDetails.verificationData.source,
      transactionDate:
          int.tryParse(purchaseDetails.transactionDate ?? '0') ?? 0,
    );

    try {
      final response =
          await _subscriptionService.appStorePurchaseResponse(payload);
      _isApplePurchaseApp = response.statusCode == 200;
      return _isApplePurchaseApp;
    } catch (e) {
      log("Error processing Apple purchase: $e");
      _isApplePurchaseApp = false;
      return false;
    }
  }

  int? _getSubscriptionIdForProductId(String productId) {
    return _subscriptionDetails?.data?.subscriptionDetails
        ?.firstWhere((detail) => detail.productId == productId,
            orElse: () => SubscriptionDetail())
        .subscriptionId;
  }

  Future<bool> isActiveSubscription() async {
    try {
      final response = await _subscriptionService.isActiveSubscription();
      if (response.statusCode == 200) {
        _verifySubscriptionModel =
            VerifySubscriptionModel.fromJson(response.data);
        final subStatus = _verifySubscriptionModel?.data?.usubStatus;
        notifyListeners();
        return subStatus == 'ACTIVE';
      } else {
        _verifySubscriptionModel = null;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _verifySubscriptionModel = null;
      log("Error verifying subscription: $e");
      notifyListeners();
      return false;
    }
  }

  void _setPurchaseLoading(bool value) {
    _isPurchaseLoading = value;
    notifyListeners();
  }

  Future<void> purchasePlan(ProductDetails? product) async {
    _purchaseProduct = product;
    notifyListeners();
  }

  void setPurchaseProduct(ProductDetails? product) {
    _purchaseProduct = product;
    notifyListeners();
  }

  void updatePlanDetails(ProductDetails? product) {
    if (product != null) {
      _planPrice = product.price;
    }
    notifyListeners();
  }

  void setSubscribedClicked(bool value) {
    _isSubscribedClicked = value;
    notifyListeners();
  }

  Future<void> showSubscriptionOptions(BuildContext context) async {
    List<String> purchasedProductIds = await getUserPurchasedProducts();

    _displayProduct = _products
        .where((product) => product.price.toLowerCase() != 'free')
        .toList();

    log("Display Product List : $_displayProduct");

    _products = _products.where((product) {
      bool hasFreeTrial = product.price.toLowerCase() == 'free';
      if (hasFreeTrial) {
        _freeProduct.add(product);
      }

      if (purchasedProductIds.contains(product.id)) {
        return !hasFreeTrial;
      }
      return true;
    }).toList();
    log("Product List : $_products");

    log("Subscription Response : $isFreeTrial");
    log("Free Product List : $_freeProduct");

    notifyListeners();
  }

  Future<List<String>> getUserPurchasedProducts() async {
    // This is a placeholder. In a real app, you would fetch this from your backend
    // or secure storage after a successful purchase and validation.
    return [];
  }

  Future<void> updateApiTriggerValue(bool value) async {
    _verifySubscription = value;
    notifyListeners();
  }
}
