import 'dart:developer';

import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/enums/enums.dart';
import 'package:eljunto/app/core/utils/currency_helper.dart';
import 'package:eljunto/app/features/subscription/providers/subscription_provider.dart';
import 'package:eljunto/reusableWidgets/custom_button.dart';
import 'package:eljunto/reusableWidgets/previous_screen_appbar.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../core/utils/text_style.dart';
import '../models/subscription_models.dart';

class ManageSubscriptionPage extends StatefulWidget {
  const ManageSubscriptionPage({super.key});

  @override
  State createState() => _ManageSubscriptionPageState();
}

class _ManageSubscriptionPageState extends State<ManageSubscriptionPage> {
  String? subscriptionName;
  double? subscriptionValue;
  String? subscriptionCurrency;
  String currencySymbol = '';
  String? purchasePlatform;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      getSubscription();
    });
  }

  Future<void> getSubscription() async {
    final provider = Provider.of<SubscriptionProvider>(context, listen: false);
    final subscriptionDetails =
        provider.subscriptionDetails?.data?.subscriptionDetails;
    subscriptionValue = provider.verifySubscriptionModel?.data?.ubAmount;
    subscriptionName = provider.verifySubscriptionModel?.data?.usubProductId;
    subscriptionCurrency = provider.verifySubscriptionModel?.data?.ubCurrency;
    purchasePlatform = provider.verifySubscriptionModel?.data?.ubPlatform;
    log("Purchase Platform : $purchasePlatform");
    await CurrencyHelper.getSymbolsFromCode(subscriptionCurrency ?? '')
        .then((value) {
      currencySymbol = value?[0] ?? '';
      log("Currency Code : $currencySymbol");
    });
    for (var element in subscriptionDetails ?? <SubscriptionDetail>[]) {
      if (element.productId == subscriptionName) {
        subscriptionName = element.subscriptionName;
        log("Subscription Name : $subscriptionName");
      }
    }
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: const PreviousScreenAppBar(
            bookName: 'Manage Subscription',
            isSetProfile: true,
          ),
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              AppConstants.bgImagePath,
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.fitWidth,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(25.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Current Plan",
                style: lbRegular.copyWith(
                  fontSize: 18,
                  color: AppConstants.primaryColor,
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              Container(
                margin: const EdgeInsets.only(bottom: 25),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.transparent,
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(
                    color: AppConstants.primaryColor,
                    width: 2,
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          width: MediaQuery.of(context).size.width * 0.7,
                          child: Text(
                            subscriptionName ?? '',
                            overflow: TextOverflow.ellipsis,
                            style: lbRegular.copyWith(
                              fontSize: 18,
                            ),
                          ),
                        ),
                        Row(
                          children: [
                            Text(
                              currencySymbol,
                              style: lbBold.copyWith(
                                fontSize: 24,
                                color: AppConstants.primaryColor,
                              ),
                            ),
                            Text(
                              "$subscriptionValue",
                              style: lbBold.copyWith(
                                fontSize: 24,
                                color: AppConstants.primaryColor,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              CustomButton(
                text: 'Cancel Subscription',
                onPressed: () async {
                  String cancellationUrl = '';
                  if (purchasePlatform == PlatformPurchase.google.value) {
                    cancellationUrl =
                        PlatformSubscriptionLink.googlePlayLink.value;
                  } else if (purchasePlatform == PlatformPurchase.apple.value) {
                    cancellationUrl =
                        PlatformSubscriptionLink.appStoreLink.value;
                  }
                  if (mounted) {
                    setState(() {});
                  }
                  await launchUrl(
                    Uri.parse(cancellationUrl),
                    mode: LaunchMode.externalApplication,
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
