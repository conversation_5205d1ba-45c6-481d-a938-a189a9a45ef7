import 'dart:io';

import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/app/features/authentication/providers/auth_provider.dart';
import 'package:eljunto/app/features/subscription/providers/subscription_provider.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/connection_error/no_connection_tag.dart';
import 'package:eljunto/reusableWidgets/contact_us.dart';
import 'package:eljunto/reusableWidgets/version_display.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../reusableWidgets/custom_button.dart';
import '../../../core/constants.dart';
import 'widgets/subscription_list_ui.dart';

class SubscriptionPage extends StatefulWidget {
  const SubscriptionPage({super.key});

  @override
  State createState() => _SubscriptionPageState();
}

class _SubscriptionPageState extends State<SubscriptionPage> {
  bool isLogout = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider =
          Provider.of<SubscriptionProvider>(context, listen: false);
      provider.fetchProducts();
      provider.purchaseStream.listen((purchaseDetailsList) {
        if (mounted) {
          provider.handlePurchaseUpdates(purchaseDetailsList, context);
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 140,
        shape: const Border(
          bottom: BorderSide(
            color: AppConstants.primaryColor,
            width: 1.5,
          ),
        ),
        automaticallyImplyLeading: false,
        backgroundColor: AppConstants.textGreenColor,
        centerTitle: true,
        title: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              AppConstants.elJuntoLogo,
              height: 100,
              width: 80,
              filterQuality: FilterQuality.high,
              fit: BoxFit.contain,
            ),
            const VersionDisplay(),
            const SizedBox(height: 25),
          ],
        ),
      ),
      body: Consumer<SubscriptionProvider>(
        builder: (context, provider, child) {
          return Skeletonizer(
            effect: const SoldColorEffect(
              color: AppConstants.skeletonforgroundColor,
              lowerBound: 0.1,
              upperBound: 0.5,
            ),
            containersColor: AppConstants.skeletonBackgroundColor,
            enabled: provider.isLoading,
            child: Container(
              padding: EdgeInsets.zero,
              height: MediaQuery.of(context).size.height,
              width: MediaQuery.of(context).size.width,
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(
                    AppConstants.bgImagePath,
                  ),
                  filterQuality: FilterQuality.high,
                  fit: BoxFit.cover,
                ),
              ),
              child: Stack(
                children: [
                  NoConnectionTag(bottomPosition: 70),
                  Padding(
                    padding: const EdgeInsets.only(
                      left: 19.0,
                      right: 19,
                      top: 25,
                    ),
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          Text(
                            provider.isFreeTrial
                                ? 'This is the final step!'
                                : 'Free trial ended',
                            style: lbRegular.copyWith(
                              fontSize: 24,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 25),
                          Skeleton.replace(
                            replacement: SubscriptionListUI(
                              subProducts: provider.products,
                            ),
                            child: SubscriptionListUI(
                              subProducts: provider.products,
                            ),
                          ),
                          if (provider.isSubscribedClicked &&
                              provider.purchaseProduct == null)
                            Text(
                              'Please select a subscription to continue.',
                              style: lbRegular.copyWith(
                                fontSize: 12,
                                fontWeight: FontWeight.w300,
                                color: AppConstants.redColor,
                              ),
                            )
                          else
                            const SizedBox.shrink(),
                          const SizedBox(height: 20),
                          CustomLoaderButton(
                            buttonWidth: provider.isPurchaseLoading
                                ? 45.0
                                : MediaQuery.of(context).size.width,
                            buttonRadius: 30.0,
                            buttonChild: provider.isPurchaseLoading
                                ? const CircularProgressIndicator(
                                    valueColor:
                                        AlwaysStoppedAnimation(Colors.white),
                                    strokeWidth: 3.0,
                                  )
                                : Text(
                                    'Subscribe',
                                    style: lbBold.copyWith(
                                      fontSize: 18,
                                      color: AppConstants.primaryColor,
                                    ),
                                  ),
                            buttonPressed: () async {
                              if (provider.purchaseProduct == null) {
                                provider.setSubscribedClicked(true);
                                return;
                              }

                              if (provider.isPurchaseLoading) {
                                return;
                              }

                              if (provider.purchaseProduct != null) {
                                await provider
                                    .buyProduct(provider.purchaseProduct!);
                              }

                              provider.setSubscribedClicked(false);
                            },
                          ),
                          const SizedBox(height: 25),
                          _buildSubscriptionPoints(
                            provider.isFreeTrial
                                ? 'Start your 30-Day Free Trial, full access for 30 days.'
                                : Platform.isAndroid
                                    ? 'To continue enjoying full access, please click above to subscribe through the play store.'
                                    : 'To continue enjoying full access, please click above to subscribe through the app store.',
                          ),
                          const SizedBox(height: 10),
                          _buildSubscriptionPoints(
                              'Only subscribers have unlimited access to all features.'),
                          const SizedBox(height: 10),
                          _buildSubscriptionPoints(
                              'Start or join a book club with integrated videoconferencing and messaging.'),
                          const SizedBox(height: 10),
                          _buildSubscriptionPoints(
                              'Search to see who is into your favorite books or author.'),
                          const SizedBox(height: 10),
                          _buildSubscriptionPoints(
                              'Customer support for any app feedback or technical issues.'),
                          const SizedBox(height: 10),
                          _buildSubscriptionPoints(
                              'Customer support to add books to the database.'),
                          const SizedBox(height: 10),
                          _buildPP(),
                          const SizedBox(height: 20),
                          Text(
                            "${provider.planPrice} per month after 30-day free trial. Cancel anytime. Subscription auto-renews unless canceled at least 24 hours before the end of the current period.",
                            style: lbRegular.copyWith(fontSize: 18),
                          ),
                          const SizedBox(height: 25),
                          CustomLoaderLogoutButton(
                            buttonWidth: isLogout
                                ? 45.0
                                : MediaQuery.of(context).size.width,
                            buttonRadius: 30.0,
                            buttonChild: isLogout
                                ? const CircularProgressIndicator(
                                    valueColor: AlwaysStoppedAnimation(
                                      AppConstants.primaryColor,
                                    ),
                                    strokeWidth: 3.0,
                                  )
                                : Text(
                                    'Logout',
                                    style: lbBold.copyWith(
                                      fontSize: 18,
                                      color: AppConstants.primaryColor,
                                    ),
                                  ),
                            buttonPressed: () async {
                              if (provider.isPurchaseLoading) {
                                return;
                              }
                              setState(() {
                                isLogout = true;
                              });
                              if (context.mounted) {
                                await Provider.of<AuthProvider>(context,
                                        listen: false)
                                    .logout(context);
                              }
                              setState(() {
                                isLogout = false;
                              });
                            },
                          ),
                          const SizedBox(height: 25),
                          NetworkAwareTap(
                            onTap: () {
                              if (!provider.isPurchaseLoading) {
                                context.pushNamed('trial-delete-account');
                              }
                            },
                            child: Text(
                              'Delete Account',
                              textAlign: TextAlign.center,
                              style: lbRegular.copyWith(
                                fontSize: 14,
                                decoration: TextDecoration.underline,
                                decorationColor: AppConstants.redColor,
                                color: AppConstants.redColor,
                              ),
                            ),
                          ),
                          const SizedBox(height: 25),
                          const ContactUs(),
                          const SizedBox(height: 25),
                        ],
                      ),
                    ),
                  ),
                  if (provider.isPurchaseLoading)
                    const Align(
                      alignment: Alignment.center,
                      child: CircularProgressIndicator(
                        color: AppConstants.primaryColor,
                        strokeWidth: 4,
                        strokeAlign: 2,
                      ),
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildPP() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Padding(
          padding: EdgeInsets.only(top: 12.0),
          child: Icon(Icons.circle, color: Colors.black, size: 8),
        ),
        const SizedBox(width: 10),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 3.0),
            child: Text.rich(
              TextSpan(
                text: 'View our ',
                style: lbRegular.copyWith(fontSize: 18),
                children: [
                  TextSpan(
                    text: 'privacy policy',
                    style: lbRegular.copyWith(
                      fontSize: 18,
                      color: AppConstants.blueColor,
                      decoration: TextDecoration.underline,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () => launchUrl(
                            AppConstants.privacyPolicyUrl,
                          ),
                  ),
                  TextSpan(
                    text: ' and ',
                    style: lbRegular.copyWith(fontSize: 18),
                  ),
                  TextSpan(
                    text: 'terms of service',
                    style: lbRegular.copyWith(
                      fontSize: 18,
                      color: AppConstants.blueColor,
                      decoration: TextDecoration.underline,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () => launchUrl(
                            AppConstants.termsAndConditionUrl,
                          ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSubscriptionPoints(String pointText) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Padding(
          padding: EdgeInsets.only(top: 12.0),
          child: Icon(Icons.circle, color: Colors.black, size: 8),
        ),
        const SizedBox(width: 10),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 3.0),
            child: Text(
              pointText,
              style: lbRegular.copyWith(fontSize: 18),
            ),
          ),
        ),
      ],
    );
  }
}
