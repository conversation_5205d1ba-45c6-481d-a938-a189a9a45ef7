import 'dart:developer';

import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/app/features/subscription/providers/subscription_provider.dart';
import 'package:flutter/material.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:provider/provider.dart';

class SubscriptionListUI extends StatefulWidget {
  final List<ProductDetails>? subProducts;
  const SubscriptionListUI({
    super.key,
    this.subProducts,
  });

  @override
  State<SubscriptionListUI> createState() => _SubscriptionListUIState();
}

class _SubscriptionListUIState extends State<SubscriptionListUI> {
  String? selectedOutcome;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      showSubscriptionOptions();
    });
  }

  void showSubscriptionOptions() {
    final provider = Provider.of<SubscriptionProvider>(context, listen: false);
    provider.showSubscriptionOptions(context);
    if (provider.displayProduct.length == 1) {
      final product = provider.displayProduct.first;
      selectedOutcome = product.id;
      provider.updatePlanDetails(product);

      if (provider.isFreeTrial && provider.freeProduct.isNotEmpty) {
        final freeTrialProduct = provider.freeProduct.firstWhere(
          (p) => p.id == product.id,
          orElse: () => product,
        );
        provider.setPurchaseProduct(freeTrialProduct);
        selectedOutcome = freeTrialProduct.id;
        provider.updatePlanDetails(freeTrialProduct);
      } else {
        provider.setPurchaseProduct(product);
      }

      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          provider.purchasePlan(provider.purchaseProduct);
        }
      });
    }
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<SubscriptionProvider>(
      builder: (context, provider, child) {
        return Column(
          children: [
            Column(
              children: provider.displayProduct.map((product) {
                return Container(
                  margin: const EdgeInsets.only(bottom: 5),
                  decoration: BoxDecoration(
                    color: Colors.transparent,
                    borderRadius: BorderRadius.circular(15),
                    border: Border.all(
                      color: AppConstants.primaryColor,
                      width: 2,
                    ),
                  ),
                  child: ListTile(
                    contentPadding: const EdgeInsets.only(left: 15, right: 10),
                    horizontalTitleGap: 0,
                    onTap: () async {
                      if (selectedOutcome == product.id) {
                        selectedOutcome = null;
                        provider.setPurchaseProduct(null);
                        provider.updatePlanDetails(null);
                        log("Deselected Product");
                      } else {
                        selectedOutcome = product.id;
                        log("Selected Products : $selectedOutcome");
                        provider.updatePlanDetails(product);

                        if (provider.isFreeTrial) {
                          if (provider.freeProduct.isNotEmpty) {
                            for (var list in provider.freeProduct) {
                              if (list.id == product.id) {
                                log("Selected Plan : $list");
                                provider.setPurchaseProduct(list);
                                selectedOutcome = list.id;
                                provider.updatePlanDetails(list);
                                break;
                              } else {
                                log('Free Product : ${product.price}');
                                provider.setPurchaseProduct(product);
                              }
                            }
                          } else {
                            log('no free product');
                            provider.setPurchaseProduct(product);
                          }
                        } else {
                          provider.setPurchaseProduct(product);
                          log('no trial product');
                        }
                      }
                      await provider.purchasePlan(provider.purchaseProduct);
                      if (mounted) {
                        setState(() {});
                      }
                    },
                    title: Text(
                      product.title,
                      overflow: TextOverflow.ellipsis,
                      style: lbRegular.copyWith(
                        fontSize: 18,
                      ),
                    ),
                    subtitle: Text(
                      product.price,
                      style: lbBold.copyWith(
                        fontSize: 24,
                        color: AppConstants.primaryColor,
                      ),
                    ),
                    trailing: Transform.scale(
                      scale: 1.6,
                      child: Radio(
                        fillColor: WidgetStatePropertyAll(
                          AppConstants.primaryColor,
                        ),
                        value: product.id,
                        groupValue: selectedOutcome,
                        onChanged: null,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        );
      },
    );
  }
}
